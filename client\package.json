{"name": "xionxepay-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "dev:all": "concurrently \"npm run dev\""}, "dependencies": {"@burnt-labs/abstraxion": "^1.0.0-alpha.65", "@burnt-labs/ui": "^0.1.0-alpha.17", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.14.10", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.2.12", "js-cookie": "^3.0.5", "lucide-react": "^0.400.0", "next": "14.2.5", "postcss": "^8.4.39", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^2.0.5", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.3", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "concurrently": "^9.2.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "json-server": "^1.0.0-beta.3"}}