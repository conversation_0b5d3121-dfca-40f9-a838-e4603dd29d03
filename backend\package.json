{"name": "xionpos-backend", "version": "1.0.0", "description": "backend for XionPOS web3 payment solution powered by Xion Protocol", "keywords": ["xionpos", "xion", "web3", "backend"], "homepage": "https://github.com/devprinceng/xionxepay#readme", "bugs": {"url": "https://github.com/devprinceng/xionxepay/issues"}, "repository": {"type": "git", "url": "git+https://github.com/devprinceng/xionxepay.git"}, "license": "ISC", "author": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "commonjs", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "rm -rf dist && tsc", "start": "node dist/index.js", "prod": "npm run build && npm start"}, "dependencies": {"@abstract-money/actions-xion": "^0.1.3", "bcryptjs": "^3.0.2", "bullmq": "^5.56.4", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^4.2.1", "nodemailer": "^7.0.3", "viem": "^2.31.3", "uuid": "^11.1.0", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "typescript": "^5.8.3"}, "devDependencies": {"ts-node-dev": "^2.0.0"}}