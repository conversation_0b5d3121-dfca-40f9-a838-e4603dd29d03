'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DollarSign, TrendingUp, Package, Wallet, Loader2, QrCode, Plus, Copy, Download, History } from 'lucide-react'
import { motion } from 'framer-motion'
import { XionConnectButton } from '@/components/xion/xion-connect-button'
import { useXion } from '@/contexts/xion-context'
import Cookies from 'js-cookie'
import { useVendor } from '@/contexts/vendor-context'
import { useProducts } from '@/contexts/ProductContext'
import { useXion as usePaymentQR } from '@/contexts/PaymentQRContext'
import { paymentAPI, Transaction } from '@/lib/payment-api'
import { toast } from 'sonner'
import Image from 'next/image'

const VendorPage = () => {
  const router = useRouter()
  const { isConnected } = useXion()
  const { vendorProfile } = useVendor()
  const { products } = useProducts()
  const {
    paymentLinks,
    isGenerating,
    generatePaymentLink,
    clearPaymentLinks,
    copyPaymentLink,
    downloadQRCode,
    deletePaymentLink
  } = usePaymentQR()

  // State for API data
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState([
    { label: 'Total Sales', value: '$0.00', icon: DollarSign, change: '+0%' },
    { label: 'Transactions', value: '0', icon: TrendingUp, change: '+0%' },
    { label: 'Total Products', value: '0', icon: Package, change: '+0%' },
  ])

  // QR Generator state
  const [selectedProduct, setSelectedProduct] = useState('')
  const [customAmount, setCustomAmount] = useState('')
  const [description, setDescription] = useState('')
  const [showCustomForm, setShowCustomForm] = useState(false)

  // Fetch transactions and calculate stats
  useEffect(() => {
    const fetchTransactions = async () => {
      if (!isConnected) return
      
      try {
        setLoading(true)
        const allTransactions = await paymentAPI.getAllTransactions()
        // console.log('🔍 DEBUG: Transaction data structure:', allTransactions[0])
        setTransactions(allTransactions)
        
        // Calculate stats from real data
        const completedTransactions = allTransactions.filter(t => t.status === 'completed')
        const totalSales = completedTransactions.reduce((sum, t) => sum + t.amount, 0)
        const totalTransactions = allTransactions.length

        setStats([
          {
            label: 'Total Sales',
            value: `${totalSales.toFixed(6)}`,
            icon: DollarSign,
            change: '+0%' // TODO: Calculate month-over-month change
          },
          {
            label: 'Transactions',
            value: totalTransactions.toString(),
            icon: TrendingUp,
            change: '+0%' // TODO: Calculate month-over-month change
          },
          {
            label: 'Total Products',
            value: products.length.toString(),
            icon: Package,
            change: '+0%' // TODO: Calculate month-over-month change
          },
        ])
      } catch (error) {
        console.error('Failed to fetch transactions:', error)
        toast.error('Failed to load transaction data')
      } finally {
        setLoading(false)
      }
    }

    fetchTransactions()
  }, [isConnected, products])

  // Handle QR generation
  const handleGeneratePaymentLink = async (productId: string, amount: string, description: string) => {
    const product = products.find(p => p._id === productId)
    const productName = product ? product.name : 'Custom Payment'

    await generatePaymentLink(productId, productName, amount, description)

    // Reset form
    setSelectedProduct('')
    setCustomAmount('')
    setDescription('')
    setShowCustomForm(false)
  }

  return (
    <div className="space-y-6">
      {/* Xion Wallet Connection - Show only this when not connected */}
      {!isConnected ? (
        <div className="flex items-center justify-center min-h-[80vh]">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-md w-full"
          >
            <Card className="p-8 border-dashed border-2 border-aurora-blue-400">
              <div className="flex flex-col items-center space-y-6">
                <div className="bg-gradient-to-r from-aurora-blue-500 to-aurora-cyan-500 p-4 rounded-full">
                  <Wallet className="w-8 h-8 text-white" />
                </div>
                <div className="text-center">
                  <h3 className="text-2xl font-semibold text-white mb-2">Connect Your Xion Wallet</h3>
                  <p className="text-gray-400 mb-4">
                    To access your vendor dashboard and enable crypto payments, please connect your Xion wallet.
                  </p>
                </div>
                <XionConnectButton />
              </div>
            </Card>
          </motion.div>
        </div>
      ) : (
        /* Show dashboard content only when connected */
        <>
          {/* Welcome Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-2xl font-bold text-white mb-2">Welcome back, {vendorProfile?.name}!</h2>
            <p className="text-gray-400">Here&apos;s what&apos;s happening with your business today.</p>
          </motion.div>

          {/* Stats Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          >
            {stats.map((stat) => {
              const Icon = stat.icon
              return (
                <Card key={stat.label} className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">{stat.label}</p>
                      {loading ? (
                        <div className="flex items-center mt-1">
                          <Loader2 className="w-5 h-5 animate-spin text-gray-400 mr-2" />
                          <p className="text-xl font-bold text-gray-400">Loading...</p>
                        </div>
                      ) : (
                        <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                      )}
                      <p className="text-aurora-blue-400 text-sm mt-1">{stat.change} from last month</p>
                    </div>
                    <div className="bg-gradient-to-r from-aurora-blue-500 to-aurora-cyan-500 p-3 rounded-lg">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </Card>
              )
            })}
          </motion.div>

          {/* QR Generator and Recent Transactions - Side by Side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* QR Generator */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="p-6 h-full">
                <h3 className="text-lg font-medium mb-4">Generate Payment Link</h3>

                {!showCustomForm ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Select a Product</label>
                      <select
                        className="w-full p-2 rounded-md bg-gray-800 border border-gray-700 text-white"
                        value={selectedProduct}
                        onChange={(e) => setSelectedProduct(e.target.value)}
                        disabled={isGenerating}
                      >
                        <option value="">Select a product</option>
                        {products.slice().reverse().map((product) => (
                          <option key={product._id} value={product._id}>
                            {product.name} - ${product.price}
                          </option>
                        ))}
                      </select>
                    </div>

                    <Button
                      onClick={() => {
                        const product = products.find(p => p._id === selectedProduct)
                        if (product) {
                          handleGeneratePaymentLink(product._id, product.price.toString(), product.name)
                        }
                      }}
                      disabled={!selectedProduct || isGenerating}
                      className="w-full"
                    >
                      {isGenerating ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <QrCode className="w-4 h-4 mr-2" />
                      )}
                      Generate QR Code
                    </Button>

                    <div className="relative my-4">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-700"></div>
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-2 bg-gray-900 text-gray-400">OR</span>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setShowCustomForm(true)}
                      disabled={isGenerating}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create Custom Payment
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Amount (USD)</label>
                      <input
                        type="number"
                        step="0.01"
                        className="w-full p-2 rounded-md bg-gray-800 border border-gray-700 text-white"
                        placeholder="0.00"
                        value={customAmount}
                        onChange={(e) => setCustomAmount(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Description</label>
                      <input
                        type="text"
                        className="w-full p-2 rounded-md bg-gray-800 border border-gray-700 text-white"
                        placeholder="Payment description"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                      />
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => handleGeneratePaymentLink('custom', customAmount, description)}
                        disabled={!customAmount || !description || isGenerating}
                        className="flex-1"
                      >
                        {isGenerating ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <QrCode className="w-4 h-4 mr-2" />
                        )}
                        Generate
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowCustomForm(false)}
                        className="flex-1"
                      >
                        Back
                      </Button>
                    </div>
                  </div>
                )}
              </Card>
            </motion.div>

            {/* Recent Payment Links */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="p-6 h-full">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <History className="w-5 h-5 text-blue-400" />
                    <h3 className="text-lg font-medium">Recent Payment Links</h3>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearPaymentLinks}
                    disabled={paymentLinks.length === 0}
                  >
                    Clear All
                  </Button>
                </div>

                {paymentLinks.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <QrCode className="w-12 h-12 text-gray-600 mb-3" />
                    <p className="text-gray-400 mb-4">No payment links generated yet</p>
                    <p className="text-sm text-gray-500 max-w-xs">Generate your first payment link and it will appear here</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2 -mr-2">
                    {paymentLinks.slice(0, 3).map((link) => (
                      <div key={link.id} className="flex items-center gap-4 p-4 bg-gray-800 rounded-lg hover:bg-gray-750 transition-colors">
                        {/* QR Code Display */}
                        <div className="flex-shrink-0">
                          {link.qrCodeData ? (
                            <Image
                              src={link.qrCodeData}
                              alt="Payment QR Code"
                              width={60}
                              height={60}
                              className="rounded border border-gray-600"
                            />
                          ) : (
                            <div className="w-[60px] h-[60px] bg-gray-700 rounded flex items-center justify-center">
                              <QrCode className="w-6 h-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Payment Details */}
                        <div className="flex-1 min-w-0">
                          <p className="text-white font-medium truncate">{link.description}</p>
                          <p className="text-aurora-blue-400 font-bold">${link.amount}</p>
                          <p className="text-gray-400 text-xs">{new Date(link.createdAt).toLocaleString()}</p>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyPaymentLink(link.link)}
                            className="p-2"
                            title="Copy payment link"
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => downloadQRCode(link.id)}
                            className="p-2"
                            title="Download QR code"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {paymentLinks.length > 3 && (
                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={() => router.push('/vendor/qr')}
                  >
                    View All Payment Links
                  </Button>
                )}
              </Card>
            </motion.div>
          </div>

          {/* Recent Transactions - Full Width Below */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="max-w-4xl mx-auto"
          >
              <Card className="p-6">
                <h2 className="text-xl font-bold text-white mb-4">Recent Transactions</h2>
                <div className="space-y-4">
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                      <p className="text-gray-400">Loading transactions...</p>
                    </div>
                  ) : transactions.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-400 mb-2">No transactions yet</p>
                      <p className="text-gray-500 text-sm">Your recent transactions will appear here</p>
                    </div>
                  ) : (
                    transactions.slice(0, 3).map((transaction) => {
                      const getStatusColor = (status: string) => {
                        switch (status) {
                          case 'completed': return 'text-green-400'
                          case 'pending': return 'text-yellow-400'
                          case 'processing': return 'text-blue-400'
                          case 'failed': return 'text-red-400'
                          case 'expired': return 'text-gray-400'
                          default: return 'text-gray-400'
                        }
                      }

                      const formatTime = (dateString: string) => {
                        if (!dateString) return 'Unknown'

                        const date = new Date(dateString)
                        if (isNaN(date.getTime())) return 'Invalid date'

                        const now = new Date()
                        const diffMs = now.getTime() - date.getTime()
                        const diffMins = Math.floor(diffMs / (1000 * 60))
                        const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
                        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

                        if (diffMs < 0) return 'In the future'
                        if (diffMins < 1) return 'Just now'
                        if (diffMins < 60) return `${diffMins} min ago`
                        if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
                        if (diffDays < 30) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
                        return date.toLocaleDateString()
                      }

                      return (
                        <div key={transaction._id} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{transaction.description}</p>
                            <p className="text-gray-400 text-sm">{formatTime((transaction as any).createdAt || (transaction as any).updatedAt)}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-aurora-blue-400 font-bold">{transaction.amount.toFixed(6)}</p>
                            <p className={`text-sm capitalize ${getStatusColor(transaction.status)}`}>{transaction.status}</p>
                          </div>
                        </div>
                      )
                    })
                  )}
                </div>
                <Button
                  variant="outline"
                  className="w-full mt-4"
                  onClick={() => router.push('/vendor/transactions')}
                >
                  View All Transactions
                </Button>
              </Card>
          </motion.div>
        </>
      )}
    </div>
  )
}

export default VendorPage
